const { AlipaySdk } = require('alipay-sdk');
const logger = require('../log/logger');

class AlipayService {
  constructor() {
    // 处理私钥格式
    const privateKey = this.formatPrivateKey(process.env.alipayPrivateKey);
    const publicKey = this.formatPublicKey(process.env.alipayPublicKey);

    this.alipaySdk = new AlipaySdk({
      // appId: process.env.alipayAppID,
      appId: "2021005177633144",
      // privateKey: privateKey,
      // alipayPublicKey: publicKey,
      // privateKey: process.env.alipayPrivateKey,
      // alipayPublicKey: process.env.alipayPublicKey,
      privateKey: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCLNCtnxIpSZ7K2pOyj+QB1OWMqzXL5l0rJemhtqj1ZfpCpIa5TczNM6ppcMix0ESdAy6TP04u+TsynFECirWNjnR+ZK6DjjIW7dd7yuufY6neDzHuOm9y1v5haNvUa9mbxt/cQOtkonLVEMLajaT/DCC9aLTOX9PkhR5Vu3Q2vlZTRxZHlpK1ZkxsBSJ98zuVOaImJuWu5BChY0eX56XaGy+11qFZe3B/FRjlQMOPOYgdj5w07g8tScjRysHPeAAZixpSONMeUMNIPZ/vrz6PyVynUaz/SVRQivp/AEceR0Az9/zkEIiWKApGHfQvEK6qx6pgcyjbxvD+bHMGJVkCpAgMBAAECggEAfH0GSEFy9ij4oPAzFJ0NNMhCkDe1l9IeNrp6g4BYZx2R3z9YuBZxDVtM+iCAsrfYY9D6rkVgVqv/x3eMKi9nj+EnLgLfWkAEJNZPsMFHsCxW/44x/SIUKpGSfTNi1V8ARzEfCOun4vLqBHaQDumD9TXvqvR7jGyvE30B/WhA4gFD00eXFrPGwz/25vazb9lwodlv7AkPQvl7OigIRz0Utlz9HGgXpGs6LpyKbdCHhuzBMNGkEuJpElh+C7efJ8qNCSMgCo3MjRytgHDHPmOcCv8S7rZ939S2p6Dcovf1k/jUxl9Oak1vmzBeG/ju5SoCpdNkG9T0y8G9u3bQCcMIhQKBgQD4n+hR27zJ3yxAJF6sb0TMJKffppyC3+VNLTKCkMyrpaBU2LozBpR+snb9CV4Dp9Unqp9W/nD6tB19iKfYPWOJsH84HbESzP7m5CfDLSvqKr8aNPGBAf0pBtiC6ABNbxT/lbpsj134HM3neE92BuOYBmAd3ft8JQ75QZK+0HwdcwKBgQCPVU3Hjor/h2OJ/JZjoSd35LwQsE4kwQ8I3EumGnQGTsRvyeJSlg039t1dk4Hd0O3MO0Zamfos9SQcYDxSBYd6ZVdhwtQtE13HWNeTgDR7O42js3l2l9QeTLQwwPfSZPSXm5L30pgvHl1dBUe4BLppvbl1khhOAj0J6On9RLVicwKBgQDBTUKJcnjj03pKHHznQFOTDOTghF+neYCEcqGU1hPrWIjBrweF6JXHs+XDcmcAHJsjjgr4zjb1FNjg40DsZT5grTMcZClqXqvIxJcNrq84DygprgQes3crUMFBdjPWAImo9bbN9OGwqGSGlQni0BRf/IT6c761lPKBZTQ820fEdQKBgG/jz4vwomFHd1TIcT3lF8JoNMdyWg4nB22LSyKnk9s4se/yUVLXAAqoE7Zw5/gYmQnNW+ZuPPXLiBz+oxgEaPYLFmOii6+EZrividoDQvTQUIlyGWeSrmvYxPEqMktzs9yF/fyn0VU3BvsOnCXyyvKwwI4fNgnvhWavA7pKYK4xAoGBAPbqzWj+szonHnqZAFlwuCcll/k2g60sPsH4lnvYvi8HBKGqHF/8wUmwuMLJyh9PMfz/u7IfF5efHDCLUsC+Hc4m+UA+RMiSCf0N0Uj7aEyVeGq0MMArSJaCBod82kepT6dbAjMry9+fOhjzTjbSm2QZibmvmtxCS3SZovP+e+7s",
      alipayPublicKey: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAizQrZ8SKUmeytqTso/kAdTljKs1y+ZdKyXpobao9WX6QqSGuU3MzTOqaXDIsdBEnQMukz9OLvk7MpxRAoq1jY50fmSug44yFu3Xe8rrn2Op3g8x7jpvctb+YWjb1GvZm8bf3EDrZKJy1RDC2o2k/wwgvWi0zl/T5IUeVbt0Nr5WU0cWR5aStWZMbAUiffM7lTmiJiblruQQoWNHl+el2hsvtdahWXtwfxUY5UDDjzmIHY+cNO4PLUnI0crBz3gAGYsaUjjTHlDDSD2f768+j8lcp1Gs/0lUUIr6fwBHHkdAM/f85BCIligKRh30LxCuqseqYHMo28bw/mxzBiVZAqQIDAQAB",
      gateway: "https://openapi.alipay.com/gateway.do",
      timeout: 5000 // 30秒超时
    });
  }

  /**
   * 格式化私钥
   * @param {string} key 私钥字符串
   * @returns {string} 格式化后的私钥
   */
  formatPrivateKey(key) {
    if (!key) return '';
    if (key.includes('-----BEGIN')) return key;
    return `-----BEGIN RSA PRIVATE KEY-----\n${key}\n-----END RSA PRIVATE KEY-----`;
  }

  /**
   * 格式化公钥
   * @param {string} key 公钥字符串
   * @returns {string} 格式化后的公钥
   */
  formatPublicKey(key) {
    if (!key) return '';
    if (key.includes('-----BEGIN')) return key;
    return `-----BEGIN PUBLIC KEY-----\n${key}\n-----END PUBLIC KEY-----`;
  }

  /**
   * 创建支付宝扫码支付订单
   * @param {Object} order 订单信息
   * @returns {Object} 支付结果
   */
  async createPayment(order) {
    try {
        // 1. 参数校验
        if (!order?.orderId || !order?.amount || !order?.subject) {
            throw new Error('缺少必要参数: orderId, amount或subject');
        }

        // 2. 金额格式校验
        if (isNaN(Number(order.amount)) || Number(order.amount) <= 0) {
            throw new Error('金额必须为大于0的数字');
        }

        // 3. 准备请求参数
        const requestParams = {
            notify_url: `${process.env.VITE_BACKEND_SRV_URL}/api/payment/notify`,
            bizContent: {
                out_trade_no: order.orderId,
                total_amount: Number(order.amount).toFixed(2), // 确保两位小数
                subject: order.subject,
                store_id: 'TopMeans_Store',
                timeout_express: '15m'
            }
        };

        logger.debug('支付宝请求参数:', {
            ...requestParams,
            bizContent: {
                ...requestParams.bizContent,
                // 隐藏敏感信息
                notify_url: requestParams.notify_url ? '已设置' : '未设置'
            }
        });

        // 4. 调用支付宝接口
        const result = await this.alipaySdk.exec('alipay.trade.precreate', requestParams);
        
        logger.debug('支付宝原始响应:', result);

        // 5. 处理响应
        if (result.code === '10000') {
            return {
                success: true,
                qrCode: result.qr_code,
                orderId: order.orderId,
                message: '订单创建成功',
                rawResponse: result // 原始响应数据
            };
        } else {
            // 支付宝返回的业务错误
            const errorInfo = this.parseAlipayError(result);
            console.error('--- 支付宝错误调试 ---');
            console.error('orderId:', order.orderId);
            console.error('errorCode:', errorInfo.code);
            console.error('errorMsg:', errorInfo.message);
            console.error('subCode:', errorInfo.subCode);
            console.error('subMsg:', errorInfo.subMsg);
            console.error('requestParams:', Object.keys(requestParams));
            console.error('notify_url:', requestParams.notify_url);
            console.error('bizContent:', Object.keys(requestParams.bizContent || {}));
            logger.error('支付宝接口业务错误:', {
                orderId: order.orderId,
                errorCode: errorInfo.code,
                errorMsg: errorInfo.message,
                subCode: errorInfo.subCode,
                subMsg: errorInfo.subMsg,
                requestParams: requestParams
            });

            return {
                success: false,
                isAlipayError: true,
                message: '支付宝接口返回错误',
                ...errorInfo,
                rawResponse: result
            };
        }
    } catch (error) {
        // 捕获所有异常
        logger.error('创建支付订单异常:', {
            errorName: error.name,
            errorMessage: error.message,
            errorStack: error.stack,
            orderDetails: {
                orderId: order?.orderId,
                amount: order?.amount,
                subject: order?.subject
            },
            // 如果是支付宝SDK错误，记录更多信息
            ...(error.response ? {
                status: error.response.status,
                data: error.response.data,
                headers: error.response.headers
            } : {})
        });

        // 返回结构化的错误信息
        return {
            success: false,
            isAlipayError: false,
            errorType: this.getErrorType(error),
            message: this.getErrorMessage(error),
            detail: {
                name: error.name,
                message: error.message,
                ...(error.response ? {
                    status: error.response.status,
                    data: error.response.data
                } : {})
            }
        };
    }
  }

  /**
   * 解析支付宝错误响应
   */
  parseAlipayError(result) {
      // 常见错误码解析
      const errorMap = {
          '20000': '服务不可用',
          '20001': '授权权限不足',
          '40001': '缺少必选参数',
          '40002': '非法的参数',
          '40004': '业务处理失败',
          '40006': '权限不足'
      };

      return {
          code: result.code,
          message: errorMap[result.code] || result.msg || '支付宝接口错误',
          subCode: result.sub_code,
          subMsg: result.sub_msg || '无子错误信息'
      };
  }

  /**
   * 获取错误类型
   */
  getErrorType(error) {
      if (error.response) {
          return 'API_RESPONSE_ERROR';
      }
      if (error.request) {
          return 'API_REQUEST_ERROR';
      }
      if (error.code === 'ECONNABORTED') {
          return 'TIMEOUT_ERROR';
      }
      return 'UNKNOWN_ERROR';
  }

  /**
   * 获取友好的错误消息
   */
  getErrorMessage(error) {
      if (error.code === 'ECONNABORTED') {
          return '支付宝接口请求超时';
      }
      if (error.response) {
          return `支付宝接口异常: ${error.message}`;
      }
      if (error.request) {
          return '无法连接到支付宝服务';
      }
      return `支付处理失败: ${error.message}`;
  }
  /////////////////////////////////////

  /**
   * 查询支付宝订单状态
   * @param {string} orderId 订单号
   * @returns {Object} 查询结果
   */
  async queryPayment(orderId) {
    try {
      logger.info('查询支付宝订单状态:', orderId);
      
      const result = await this.alipaySdk.exec('alipay.trade.query', {
        bizContent: {
          out_trade_no: orderId
        }
      });
      
      logger.info('支付宝查询响应:', result);
      
      if (result.code === '10000') {
        return {
          success: true,
          tradeStatus: result.trade_status,
          tradeNo: result.trade_no,
          totalAmount: result.total_amount,
          buyerPayAmount: result.buyer_pay_amount,
          sendPayDate: result.send_pay_date
        };
      } else {
        logger.error('支付宝查询API错误:', result);
        return {
          success: false,
          message: result.msg || '查询订单状态失败'
        };
      }
    } catch (error) {
      logger.error('查询支付状态失败:', error);
      return {
        success: false,
        message: error.message || '查询订单状态失败'
      };
    }
  }

  /**
   * 验证支付宝异步通知签名
   * @param {Object} params 通知参数
   * @returns {boolean} 验证结果
   */
  verifyNotify(params) {
    try {
      return this.alipaySdk.checkNotifySign(params);
    } catch (error) {
      logger.error('验证支付宝通知签名失败:', error);
      return false;
    }
  }

  /**
   * 关闭订单
   * @param {string} orderId 订单号
   * @returns {Object} 关闭结果
   */
  async closeOrder(orderId) {
    try {
      logger.info('关闭支付宝订单:', orderId);
      
      const result = await this.alipaySdk.exec('alipay.trade.close', {
        bizContent: {
          out_trade_no: orderId
        }
      });
      
      if (result.code === '10000') {
        return {
          success: true,
          message: '订单关闭成功'
        };
      } else {
        return {
          success: false,
          message: result.msg || '关闭订单失败'
        };
      }
    } catch (error) {
      logger.error('关闭订单失败:', error);
      return {
        success: false,
        message: error.message || '关闭订单失败'
      };
    }
  }
}

module.exports = new AlipayService();
