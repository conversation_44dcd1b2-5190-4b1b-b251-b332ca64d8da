const { AlipaySdk } = require('alipay-sdk');
const logger = require('../log/logger');

class AlipayService {
  constructor() {
    this.alipaySdk = new AlipaySdk({
      appId: process.env.alipayAppID,
      privateKey: process.env.alipayPrivateKey,
      alipayPublicKey: process.env.alipayPublicKey,
      gateway: process.env.ALIPAY_GATEWAY || "https://openapi.alipay.com/gateway.do",
      signType: 'RSA2',
      charset: 'utf-8',
      version: '1.0'
    });
  }

  /**
   * 创建支付宝扫码支付订单
   * @param {Object} order 订单信息
   * @returns {Object} 支付结果
   */
  async createPayment(order) {
    try {
      logger.info('创建支付宝支付订单:', order);

      // 开发环境模拟模式
      if (process.env.NODE_ENV === 'development' || !process.env.alipayAppID || process.env.alipayAppID === '2021005177633144') {
        logger.info('使用模拟支付模式');

        // 生成模拟二维码内容
        const mockQrCode = `https://qr.alipay.com/mock_payment?orderId=${order.orderId}&amount=${order.amount}`;

        return {
          success: true,
          qrCode: mockQrCode,
          orderId: order.orderId,
          message: '订单创建成功（模拟模式）'
        };
      }

      const result = await this.alipaySdk.exec('alipay.trade.precreate', {
        notify_url: `${process.env.VITE_BACKEND_SRV_URL}/api/payment/notify`,
        bizContent: {
          out_trade_no: order.orderId,
          total_amount: order.amount.toString(),
          subject: order.subject,
          store_id: 'TopMeans_Store',
          timeout_express: '15m'
        }
      });

      logger.info('支付宝API响应:', result);

      if (result.code === '10000') {
        return {
          success: true,
          qrCode: result.qr_code,
          orderId: order.orderId,
          message: '订单创建成功'
        };
      } else {
        logger.error('支付宝API错误:', result);
        return {
          success: false,
          message: result.msg || '创建支付订单失败'
        };
      }
    } catch (error) {
      logger.error('创建支付失败:', error);
      return {
        success: false,
        message: error.message || '创建支付订单失败'
      };
    }
  }

  /**
   * 查询支付宝订单状态
   * @param {string} orderId 订单号
   * @returns {Object} 查询结果
   */
  async queryPayment(orderId) {
    try {
      logger.info('查询支付宝订单状态:', orderId);
      
      const result = await this.alipaySdk.exec('alipay.trade.query', {
        bizContent: {
          out_trade_no: orderId
        }
      });
      
      logger.info('支付宝查询响应:', result);
      
      if (result.code === '10000') {
        return {
          success: true,
          tradeStatus: result.trade_status,
          tradeNo: result.trade_no,
          totalAmount: result.total_amount,
          buyerPayAmount: result.buyer_pay_amount,
          sendPayDate: result.send_pay_date
        };
      } else {
        logger.error('支付宝查询API错误:', result);
        return {
          success: false,
          message: result.msg || '查询订单状态失败'
        };
      }
    } catch (error) {
      logger.error('查询支付状态失败:', error);
      return {
        success: false,
        message: error.message || '查询订单状态失败'
      };
    }
  }

  /**
   * 验证支付宝异步通知签名
   * @param {Object} params 通知参数
   * @returns {boolean} 验证结果
   */
  verifyNotify(params) {
    try {
      return this.alipaySdk.checkNotifySign(params);
    } catch (error) {
      logger.error('验证支付宝通知签名失败:', error);
      return false;
    }
  }

  /**
   * 关闭订单
   * @param {string} orderId 订单号
   * @returns {Object} 关闭结果
   */
  async closeOrder(orderId) {
    try {
      logger.info('关闭支付宝订单:', orderId);
      
      const result = await this.alipaySdk.exec('alipay.trade.close', {
        bizContent: {
          out_trade_no: orderId
        }
      });
      
      if (result.code === '10000') {
        return {
          success: true,
          message: '订单关闭成功'
        };
      } else {
        return {
          success: false,
          message: result.msg || '关闭订单失败'
        };
      }
    } catch (error) {
      logger.error('关闭订单失败:', error);
      return {
        success: false,
        message: error.message || '关闭订单失败'
      };
    }
  }
}

module.exports = new AlipayService();
