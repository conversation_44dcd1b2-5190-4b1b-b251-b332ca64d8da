<template>
  <div class="payment-methods">
    <el-card class="payment-card">
      <template #header>
        <div class="card-header">
          <span>选择支付方式</span>
        </div>
      </template>

      <el-radio-group v-model="selectedMethod" class="payment-methods-group">
        <el-radio-button label="alipay">
          <img :src="'/images/alipay.png?url'" alt="支付宝" class="payment-icon" />
          <span>支付宝</span>
        </el-radio-button>
        <el-radio-button label="wechat">
          <img :src="'/images/wechat.png?url'" alt="微信支付" class="payment-icon" />
          <span>微信支付</span>
        </el-radio-button>
      </el-radio-group>

      <div class="payment-amount">
        <span class="amount-label">支付金额：</span>
        <span class="amount-value">¥{{ amount }}</span>
      </div>

      <!-- 充值推荐 -->
      <div class="recharge-recommendation">
        <div class="recommendation-header">
          <span class="recommendation-icon">💡</span>
          <span class="recommendation-title">充值有优惠</span>
        </div>
        <div class="recharge-options">
          <div class="recharge-option" @click="handleRecharge(50)">
            <span class="option-amount">¥50</span>
            <span class="option-bonus">送¥10</span>
          </div>
          <div class="recharge-option" @click="handleRecharge(100)">
            <span class="option-amount">¥100</span>
            <span class="option-bonus">送¥25</span>
          </div>
          <div class="recharge-option" @click="handleRecharge(200)">
            <span class="option-amount">¥200</span>
            <span class="option-bonus">送¥60</span>
          </div>
        </div>
        <p class="recharge-note">充值后余额可用于后续服务，享受更多优惠</p>
      </div>

      <div class="payment-actions">
        <el-button type="primary" @click="handlePayment" :loading="loading">
          确认支付
        </el-button>
      </div>
    </el-card>

    <!-- 支付二维码弹窗 -->
    <el-dialog
      v-model="qrCodeVisible"
      title="扫码支付"
      width="350px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="qr-code-container">
        <div class="payment-info">
          <p class="amount-display">支付金额：<span class="amount">¥{{ amount }}</span></p>
          <p class="order-id" v-if="currentOrderId">订单号：{{ currentOrderId }}</p>
        </div>

        <div class="payment-wrapper">
          <div class="qr-code-wrapper">
            <canvas ref="qrCodeCanvas" class="qr-code-canvas"></canvas>
          </div>
        </div>

        <div class="payment-status">
          <p v-if="paymentStatus === 'pending'" class="status-pending">
            <el-icon class="loading-icon"><Loading /></el-icon>
            请使用{{ selectedMethod === 'alipay' ? '支付宝' : '微信' }}扫码支付
          </p>
          <p v-else-if="paymentStatus === 'paid'" class="status-success">
            <el-icon class="success-icon"><SuccessFilled /></el-icon>
            支付成功！
          </p>
          <p v-else-if="paymentStatus === 'closed'" class="status-closed">
            订单已关闭
          </p>
        </div>

        <div class="payment-tips">
          <p>• 请在15分钟内完成支付</p>
          <p>• 支付完成后页面将自动跳转</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelPayment" :disabled="paymentStatus === 'paid'">
            {{ paymentStatus === 'paid' ? '支付成功' : '取消支付' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, SuccessFilled } from '@element-plus/icons-vue'
import QRCode from 'qrcode'

const props = defineProps({
  amount: {
    type: Number,
    required: true
  },
  subject: {
    type: String,
    default: 'TopMeans服务购买'
  },
  goods: {
    type: Object,
    default: () => ({})
  },
  userId: {
    type: Number,
    default: null
  },
  userAccount: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['payment-success', 'payment-cancel'])

const selectedMethod = ref('alipay')
const loading = ref(false)
const qrCodeVisible = ref(false)
const qrCodeCanvas = ref(null)
const currentOrderId = ref('')
const paymentStatus = ref('')
const statusCheckInterval = ref(null)
const paymentMode = ref('qrcode') // 统一使用二维码模式

// 获取后端服务URL
const getBackendUrl = () => {
  return import.meta.env.VITE_BACKEND_SRV_URL || 'http://localhost:3999'
}

const handlePayment = async () => {
  if (selectedMethod.value !== 'alipay') {
    ElMessage.warning('暂时只支持支付宝支付')
    return
  }

  loading.value = true
  try {
    // 调用后端接口创建支付订单
    const response = await fetch(`${getBackendUrl()}/api/payment/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        amount: props.amount,
        subject: props.subject,
        goods: props.goods,
        userId: props.userId,
        userAccount: props.userAccount,
        paymentMethod: selectedMethod.value
      })
    })

    const data = await response.json()

    if (data.success) {
      currentOrderId.value = data.orderId

      // 检查返回的是HTML表单还是二维码URL
      if (data.paymentUrl && data.paymentUrl.includes('<form')) {
        // HTML表单模式 - 提取支付URL生成二维码在弹窗中显示
        paymentMode.value = 'qrcode'

        // 从HTML表单中提取支付URL
        const paymentUrl = extractPaymentUrlFromForm(data.paymentUrl)
        if (paymentUrl) {
          await generateQRCode(paymentUrl)

          qrCodeVisible.value = true
          paymentStatus.value = 'pending'

          // 开始轮询支付状态
          startStatusCheck()

          ElMessage.success('订单创建成功，请扫码支付')
        } else {
          ElMessage.error('无法提取支付链接，请重试')
        }
      } else {
        // 二维码模式
        paymentMode.value = 'qrcode'
        await generateQRCode(data.paymentUrl)

        qrCodeVisible.value = true
        paymentStatus.value = 'pending'

        // 开始轮询支付状态
        startStatusCheck()

        ElMessage.success('订单创建成功，请扫码支付')
      }
    } else {
      ElMessage.error(data.message || '创建支付订单失败')
    }
  } catch (error) {
    console.error('支付请求失败:', error)
    ElMessage.error('网络请求失败，请重试')
  } finally {
    loading.value = false
  }
}

const extractPaymentUrlFromForm = (htmlForm) => {
  try {
    // 创建一个临时的DOM元素来解析HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = htmlForm

    // 查找表单元素
    const form = tempDiv.querySelector('form')
    if (form) {
      const action = form.getAttribute('action')
      if (action) {
        console.log('提取到的支付URL:', action)
        return action
      }
    }

    // 如果没有找到表单，尝试从HTML中提取URL
    const urlMatch = htmlForm.match(/action="([^"]+)"/)
    if (urlMatch && urlMatch[1]) {
      console.log('通过正则提取到的支付URL:', urlMatch[1])
      return urlMatch[1]
    }

    return null
  } catch (error) {
    console.error('提取支付URL失败:', error)
    return null
  }
}

const generateQRCode = async (qrCodeData) => {
  try {
    if (qrCodeCanvas.value) {
      await QRCode.toCanvas(qrCodeCanvas.value, qrCodeData, {
        width: 200,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
    }
  } catch (error) {
    console.error('生成二维码失败:', error)
    ElMessage.error('生成二维码失败')
  }
}

const startStatusCheck = () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }

  statusCheckInterval.value = setInterval(async () => {
    try {
      const response = await fetch(`${getBackendUrl()}/api/payment/status/${currentOrderId.value}`)
      const data = await response.json()

      if (data.success) {
        if (data.status === 'paid') {
          paymentStatus.value = 'paid'
          clearInterval(statusCheckInterval.value)

          ElMessage.success('支付成功！')

          setTimeout(() => {
            qrCodeVisible.value = false
            emit('payment-success', {
              orderId: currentOrderId.value,
              amount: props.amount,
              alipayTradeNo: data.alipayTradeNo
            })
          }, 2000)
        } else if (data.status === 'closed' || data.status === 'cancelled') {
          paymentStatus.value = data.status
          clearInterval(statusCheckInterval.value)
          ElMessage.warning('订单已关闭')
        }
      }
    } catch (error) {
      console.error('查询支付状态失败:', error)
    }
  }, 3000) // 每3秒查询一次
}

const cancelPayment = async () => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }

  // 如果有订单ID，尝试取消订单
  if (currentOrderId.value) {
    try {
      await fetch(`${getBackendUrl()}/api/payment/cancel/${currentOrderId.value}`, {
        method: 'POST'
      })
    } catch (error) {
      console.error('取消订单失败:', error)
    }
  }

  qrCodeVisible.value = false
  paymentStatus.value = ''
  currentOrderId.value = ''
  emit('payment-cancel')
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (statusCheckInterval.value) {
    clearInterval(statusCheckInterval.value)
  }
})
</script>

<style scoped>
.payment-methods {
  padding: 20px;
}

.payment-card {
  max-width: 600px;
  margin: 0 auto;
}

.card-header {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
}

.payment-methods-group {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  vertical-align: middle;
}

.payment-amount {
  text-align: center;
  margin: 20px 0;
  font-size: 18px;
}

.amount-label {
  color: #666;
}

.amount-value {
  color: #f56c6c;
  font-weight: bold;
  font-size: 24px;
}

.payment-actions {
  text-align: center;
  margin-top: 20px;
}

.qr-code-container {
  text-align: center;
  padding: 20px 0;
}

.payment-info {
  margin-bottom: 20px;
}

.amount-display {
  font-size: 16px;
  margin-bottom: 8px;
}

.amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 20px;
}

.order-id {
  font-size: 12px;
  color: #999;
  margin: 0;
}

.payment-wrapper {
  margin: 20px 0;
}

.qr-code-wrapper {
  display: flex;
  justify-content: center;
}

.qr-code-canvas {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.redirect-wrapper {
  text-align: center;
}

.payment-form {
  display: none; /* 隐藏表单，因为会自动提交 */
}

.payment-status {
  margin: 20px 0;
}

.status-pending {
  color: #409eff;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.status-success {
  color: #67c23a;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.status-closed {
  color: #f56c6c;
  font-size: 14px;
}

.loading-icon {
  animation: rotate 2s linear infinite;
}

.success-icon {
  font-size: 16px;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.payment-tips {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e4e7ed;
}

.payment-tips p {
  font-size: 12px;
  color: #999;
  margin: 5px 0;
  text-align: left;
}
</style> 