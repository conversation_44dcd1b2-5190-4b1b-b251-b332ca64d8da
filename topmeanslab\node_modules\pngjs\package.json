{"name": "pngjs", "version": "5.0.0", "description": "PNG encoder/decoder in pure JS, supporting any bit size & interlace, async & sync with full test suite.", "contributors": ["<PERSON>", "Gaurav Mali", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "lian<PERSON><PERSON>g", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toriningen", "<PERSON>"], "homepage": "https://github.com/lukeapage/pngjs", "keywords": ["PNG", "decoder", "encoder", "js-png", "node-png", "parser", "png", "png-js", "png-parse", "pngjs"], "engines": {"node": ">=10.13.0"}, "main": "./lib/png.js", "directories": {"lib": "lib", "example": "examples", "test": "test"}, "scripts": {"build": "yarn prepublish", "prepublish": "yarn browserify", "browserify": "browserify lib/png.js --standalone png > browser.js", "coverage": "nyc --reporter=lcov --reporter=text-summary tape test/*-spec.js nolarge", "test": "yarn lint && yarn prettier:check && tape test/*-spec.js | tap-dot && node test/run-compare", "lint": "eslint .", "prettier:write": "prettier --write .", "prettier:check": "prettier --check ."}, "repository": {"type": "git", "url": "git://github.com/lukeapage/pngjs.git"}, "license": "MIT", "bugs": {"url": "https://github.com/lukeapage/pngjs/issues"}, "devDependencies": {"browserify": "16.5.1", "buffer-equal": "1.0.0", "codecov": "3.6.5", "connect": "3.7.0", "eslint": "6.8.0", "eslint-config-prettier": "6.10.1", "nyc": "15.0.1", "prettier": "2.0.4", "puppeteer": "2.1.1", "serve-static": "1.14.1", "tap-dot": "2.0.0", "tape": "4.13.2"}}