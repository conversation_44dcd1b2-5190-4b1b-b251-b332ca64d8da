// https://vitepress.dev/guide/custom-theme
import { h } from 'vue'
import DefaultTheme from 'vitepress/theme'
import HomeLayout from './layout/home.vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { useUserStore } from './components/UserCenter/userStore'

import './style.css'

/** @type {import('vitepress').Theme} */
export default {
  extends: DefaultTheme,
  Layout: () => {
    return h(DefaultTheme.Layout, null, {
         'home-features-after': () => h(HomeLayout)
    })
  },
  enhanceApp({ app, router, siteData }) {
    // 创建 Pinia 实例
    const pinia = createPinia()
    
    // 使用 Pinia
    app.use(pinia)
    
    // 使用 Element Plus
    app.use(ElementPlus)
    
    // 注册所有图标
    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
      app.component(key, component)
    }

    // 添加全局路由守卫
    router.onBeforeRouteChange = async (to) => {
      const userStore = useUserStore()
      await userStore.initAuthState() // 确保完成初始化
      
      // 如果访问独立登录页面且已登录，跳转到首页
      if (to === '/login' && userStore.isLoggedIn) {
        return '/'
      }

      // 用户中心相关页面需要登录
      if (to.startsWith('/user-center') && !to.includes('/login')) {
        if (!userStore.isLoggedIn) {
          return '/'
        }
      }
      
      // 服务购买页面需要登录
      if (to.startsWith('/service-purchase') && !userStore.isLoggedIn) {
        return '/' // 跳转到首页，首页会显示登录界面
      }
    }
  }
}
