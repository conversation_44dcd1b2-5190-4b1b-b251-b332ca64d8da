
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.72% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1034/1250</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">67.59% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>317/469</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">90.26% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>139/154</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.53% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>992/1202</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="bitmapper.js"><a href="bitmapper.js.html">bitmapper.js</a></td>
	<td data-value="94.37" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 94%"></div><div class="cover-empty" style="width: 6%"></div></div>
	</td>
	<td data-value="94.37" class="pct high">94.37%</td>
	<td data-value="142" class="abs high">134/142</td>
	<td data-value="72.41" class="pct medium">72.41%</td>
	<td data-value="29" class="abs medium">21/29</td>
	<td data-value="89.47" class="pct high">89.47%</td>
	<td data-value="19" class="abs high">17/19</td>
	<td data-value="94.16" class="pct high">94.16%</td>
	<td data-value="137" class="abs high">129/137</td>
	</tr>

<tr>
	<td class="file low" data-value="bitpacker.js"><a href="bitpacker.js.html">bitpacker.js</a></td>
	<td data-value="11.36" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 11%"></div><div class="cover-empty" style="width: 89%"></div></div>
	</td>
	<td data-value="11.36" class="pct low">11.36%</td>
	<td data-value="88" class="abs low">10/88</td>
	<td data-value="6.12" class="pct low">6.12%</td>
	<td data-value="49" class="abs low">3/49</td>
	<td data-value="66.67" class="pct medium">66.67%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="11.63" class="pct low">11.63%</td>
	<td data-value="86" class="abs low">10/86</td>
	</tr>

<tr>
	<td class="file high" data-value="chunkstream.js"><a href="chunkstream.js.html">chunkstream.js</a></td>
	<td data-value="90.11" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90.11" class="pct high">90.11%</td>
	<td data-value="91" class="abs high">82/91</td>
	<td data-value="81.4" class="pct high">81.4%</td>
	<td data-value="43" class="abs high">35/43</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="90.11" class="pct high">90.11%</td>
	<td data-value="91" class="abs high">82/91</td>
	</tr>

<tr>
	<td class="file high" data-value="constants.js"><a href="constants.js.html">constants.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	</tr>

<tr>
	<td class="file high" data-value="crc.js"><a href="crc.js.html">crc.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="26" class="abs high">26/26</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="22" class="abs high">22/22</td>
	</tr>

<tr>
	<td class="file high" data-value="filter-pack.js"><a href="filter-pack.js.html">filter-pack.js</a></td>
	<td data-value="95.83" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 95%"></div><div class="cover-empty" style="width: 5%"></div></div>
	</td>
	<td data-value="95.83" class="pct high">95.83%</td>
	<td data-value="96" class="abs high">92/96</td>
	<td data-value="81.82" class="pct high">81.82%</td>
	<td data-value="44" class="abs high">36/44</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="95.24" class="pct high">95.24%</td>
	<td data-value="84" class="abs high">80/84</td>
	</tr>

<tr>
	<td class="file high" data-value="filter-parse-async.js"><a href="filter-parse-async.js.html">filter-parse-async.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	</tr>

<tr>
	<td class="file high" data-value="filter-parse-sync.js"><a href="filter-parse-sync.js.html">filter-parse-sync.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	</tr>

<tr>
	<td class="file high" data-value="filter-parse.js"><a href="filter-parse.js.html">filter-parse.js</a></td>
	<td data-value="98.96" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 98%"></div><div class="cover-empty" style="width: 2%"></div></div>
	</td>
	<td data-value="98.96" class="pct high">98.96%</td>
	<td data-value="96" class="abs high">95/96</td>
	<td data-value="97.14" class="pct high">97.14%</td>
	<td data-value="35" class="abs high">34/35</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="98.9" class="pct high">98.9%</td>
	<td data-value="91" class="abs high">90/91</td>
	</tr>

<tr>
	<td class="file high" data-value="format-normaliser.js"><a href="format-normaliser.js.html">format-normaliser.js</a></td>
	<td data-value="98.21" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 98%"></div><div class="cover-empty" style="width: 2%"></div></div>
	</td>
	<td data-value="98.21" class="pct high">98.21%</td>
	<td data-value="56" class="abs high">55/56</td>
	<td data-value="95.24" class="pct high">95.24%</td>
	<td data-value="21" class="abs high">20/21</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="97.87" class="pct high">97.87%</td>
	<td data-value="47" class="abs high">46/47</td>
	</tr>

<tr>
	<td class="file high" data-value="interlace.js"><a href="interlace.js.html">interlace.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="32" class="abs high">32/32</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="29" class="abs high">29/29</td>
	</tr>

<tr>
	<td class="file high" data-value="packer-async.js"><a href="packer-async.js.html">packer-async.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="24" class="abs high">24/24</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="4" class="abs medium">3/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="4" class="abs high">4/4</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="24" class="abs high">24/24</td>
	</tr>

<tr>
	<td class="file low" data-value="packer-sync.js"><a href="packer-sync.js.html">packer-sync.js</a></td>
	<td data-value="25" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 25%"></div><div class="cover-empty" style="width: 75%"></div></div>
	</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="24" class="abs low">6/24</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="12" class="abs low">1/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="25" class="pct low">25%</td>
	<td data-value="24" class="abs low">6/24</td>
	</tr>

<tr>
	<td class="file high" data-value="packer.js"><a href="packer.js.html">packer.js</a></td>
	<td data-value="94.74" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 94%"></div><div class="cover-empty" style="width: 6%"></div></div>
	</td>
	<td data-value="94.74" class="pct high">94.74%</td>
	<td data-value="57" class="abs high">54/57</td>
	<td data-value="67.86" class="pct medium">67.86%</td>
	<td data-value="28" class="abs medium">19/28</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="9" class="abs high">9/9</td>
	<td data-value="94.74" class="pct high">94.74%</td>
	<td data-value="57" class="abs high">54/57</td>
	</tr>

<tr>
	<td class="file high" data-value="paeth-predictor.js"><a href="paeth-predictor.js.html">paeth-predictor.js</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="10" class="abs high">10/10</td>
	</tr>

<tr>
	<td class="file high" data-value="parser-async.js"><a href="parser-async.js.html">parser-async.js</a></td>
	<td data-value="88.75" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.75" class="pct high">88.75%</td>
	<td data-value="80" class="abs high">71/80</td>
	<td data-value="72.73" class="pct medium">72.73%</td>
	<td data-value="22" class="abs medium">16/22</td>
	<td data-value="84.62" class="pct high">84.62%</td>
	<td data-value="13" class="abs high">11/13</td>
	<td data-value="88.75" class="pct high">88.75%</td>
	<td data-value="80" class="abs high">71/80</td>
	</tr>

<tr>
	<td class="file high" data-value="parser-sync.js"><a href="parser-sync.js.html">parser-sync.js</a></td>
	<td data-value="93.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 93%"></div><div class="cover-empty" style="width: 7%"></div></div>
	</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="45" class="abs high">42/45</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="93.33" class="pct high">93.33%</td>
	<td data-value="45" class="abs high">42/45</td>
	</tr>

<tr>
	<td class="file high" data-value="parser.js"><a href="parser.js.html">parser.js</a></td>
	<td data-value="90.57" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90.57" class="pct high">90.57%</td>
	<td data-value="159" class="abs high">144/159</td>
	<td data-value="85.45" class="pct high">85.45%</td>
	<td data-value="55" class="abs high">47/55</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="20" class="abs high">20/20</td>
	<td data-value="90.32" class="pct high">90.32%</td>
	<td data-value="155" class="abs high">140/155</td>
	</tr>

<tr>
	<td class="file high" data-value="png-sync.js"><a href="png-sync.js.html">png-sync.js</a></td>
	<td data-value="83.33" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="6" class="abs high">5/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="83.33" class="pct high">83.33%</td>
	<td data-value="6" class="abs high">5/6</td>
	</tr>

<tr>
	<td class="file medium" data-value="png.js"><a href="png.js.html">png.js</a></td>
	<td data-value="54.17" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.17" class="pct medium">54.17%</td>
	<td data-value="96" class="abs medium">52/96</td>
	<td data-value="41.18" class="pct low">41.18%</td>
	<td data-value="34" class="abs low">14/34</td>
	<td data-value="56.25" class="pct medium">56.25%</td>
	<td data-value="16" class="abs medium">9/16</td>
	<td data-value="56.52" class="pct medium">56.52%</td>
	<td data-value="92" class="abs medium">52/92</td>
	</tr>

<tr>
	<td class="file medium" data-value="sync-inflate.js"><a href="sync-inflate.js.html">sync-inflate.js</a></td>
	<td data-value="75.31" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.31" class="pct medium">75.31%</td>
	<td data-value="81" class="abs medium">61/81</td>
	<td data-value="62" class="pct medium">62%</td>
	<td data-value="50" class="abs medium">31/50</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="8" class="abs high">7/8</td>
	<td data-value="75.31" class="pct medium">75.31%</td>
	<td data-value="81" class="abs medium">61/81</td>
	</tr>

<tr>
	<td class="file high" data-value="sync-reader.js"><a href="sync-reader.js.html">sync-reader.js</a></td>
	<td data-value="88.89" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.89" class="pct high">88.89%</td>
	<td data-value="18" class="abs high">16/18</td>
	<td data-value="72.73" class="pct medium">72.73%</td>
	<td data-value="11" class="abs medium">8/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="88.89" class="pct high">88.89%</td>
	<td data-value="18" class="abs high">16/18</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank">istanbul</a>
                at Fri Apr 10 2020 13:53:16 GMT+0200 (Central European Summer Time)
            </div>
        </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    